package com.tianji.learning.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tianji.api.client.course.CatalogueClient;
import com.tianji.api.client.course.CourseClient;
import com.tianji.api.client.user.UserClient;
import com.tianji.api.dto.course.CataSimpleInfoDTO;
import com.tianji.api.dto.course.CourseFullInfoDTO;
import com.tianji.api.dto.course.CourseSimpleInfoDTO;
import com.tianji.common.domain.dto.PageDTO;
import com.tianji.common.domain.query.PageQuery;
import com.tianji.common.exceptions.BadRequestException;
import com.tianji.common.utils.BeanUtils;
import com.tianji.common.utils.CollUtils;
import com.tianji.common.utils.UserContext;
import com.tianji.learning.domain.po.LearningLesson;
import com.tianji.learning.domain.vo.LearningLessonVO;
import com.tianji.learning.enums.LessonStatus;
import com.tianji.learning.enums.PlanStatus;
import com.tianji.learning.mapper.LearningLessonMapper;
import com.tianji.learning.service.ILearningLessonService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.C;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 学生课程表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LearningLessonServiceImpl extends ServiceImpl<LearningLessonMapper, LearningLesson> implements ILearningLessonService {

    final CourseClient courseClient;

    final CatalogueClient catalogueClient;

    /**
     * 批量新增用户课程表
     * @param userId 用户id
     * @param courseIds 课程id列表
     */
    @Override
    public void addUserLessons(Long userId, List<Long> courseIds) {
        List<LearningLesson> lessons = courseClient.getSimpleInfoList(courseIds).stream().map(course -> {
            LearningLesson lesson = new LearningLesson();
            lesson.setUserId(userId);
            lesson.setCourseId(course.getId());
            Integer validDuration = course.getValidDuration(); // 课程有效期
            if (validDuration != null && validDuration > 0) {
                LocalDateTime now = LocalDateTime.now();
                lesson.setCreateTime(now);
                lesson.setExpireTime(now.plusMonths(validDuration));
            }
            return lesson;
        }).collect(Collectors.toList());

        this.saveBatch(lessons);
    }

    /**
     * 分页查询用户课程表
     * @param query 分页参数
     * @return 用户课程表分页数据
     */
    @Override
    public PageDTO<LearningLessonVO> queryMyLessons(PageQuery query) {
        // 获取当前登录用户ID
        Long userId = UserContext.getUser();
        if (userId == null) {
            throw new RuntimeException("用户未登录");
        }

        // 分页查询用户的课程表数据，按最近学习时间倒序排列
        Page<LearningLesson> page = this.lambdaQuery()
                .eq(LearningLesson::getUserId, userId)
                .page(query.toMpPage("latest_learn_time", false));
        List<LearningLesson> records = page.getRecords();
        if (CollUtils.isEmpty(records)){
            return PageDTO.empty(page);
        }

        // 查询课程基本信息
        List<Long> courseIds = records.stream().map(LearningLesson::getCourseId).collect(Collectors.toList());
        List<CourseSimpleInfoDTO> cinfos = courseClient.getSimpleInfoList(courseIds);
        if (CollUtils.isEmpty(cinfos)){
            throw new RuntimeException("课程不存在");
        }

        // 构建课程信息映射表，方便后续关联查询
        Map<Long, CourseSimpleInfoDTO> infoDTOMap = cinfos.stream().collect(Collectors.toMap(CourseSimpleInfoDTO::getId, c -> c));

        // 组装返回结果VO列表
        List<LearningLessonVO> voList = new ArrayList<>();
        for (LearningLesson record : records){
            LearningLessonVO vo = BeanUtils.copyBean(record, LearningLessonVO.class);
            CourseSimpleInfoDTO infoDTO = infoDTOMap.get(record.getCourseId());
            if (infoDTO != null){
                vo.setCourseName(infoDTO.getName());
                vo.setCourseCoverUrl(infoDTO.getCoverUrl());
                vo.setSections(infoDTO.getSectionNum());
            }
            voList.add(vo);
        }

        return PageDTO.of(page, voList);
    }

    /**
     * 查询用户当前正在学习的课程信息
     * @return LearningLessonVO 用户当前正在学习的课程信息，如果没有正在学习的课程则返回null
     */
    @Override
    public LearningLessonVO queryMyCurrentLesson() {

        // 获取当前登录用户ID
        Long userId = UserContext.getUser();

        // 查询用户正在学习的课程，按最近学习时间倒序排列，只取第一条
        LearningLesson lesson = this.lambdaQuery()
                .eq(LearningLesson::getUserId, userId)
                .eq(LearningLesson::getStatus, LessonStatus.LEARNING)
                .orderByDesc(LearningLesson::getLatestLearnTime)
                .last("limit 1")
                .one();

        if (lesson == null){
            return null;
        }

        // 查询课程详细信息
        CourseFullInfoDTO cinfo = courseClient.getCourseInfoById(lesson.getCourseId(), false, false);
        if (cinfo == null){
            throw new RuntimeException("课程不存在");
        }

        // 统计用户报名的课程总数
        Integer count = Math.toIntExact(this.lambdaQuery().eq(LearningLesson::getUserId, userId).count());

        // 查询最近学习的小节信息
        Long latestSectionId = lesson.getLatestSectionId();
        List<CataSimpleInfoDTO> cataSimpleInfoDTOS = catalogueClient.batchQueryCatalogue(CollUtils.singletonList(latestSectionId));
        if (CollUtils.isEmpty(cataSimpleInfoDTOS)){
            throw new RuntimeException("小节不存在");
        }

        // 组装返回结果VO
        LearningLessonVO vo = BeanUtils.copyBean(lesson, LearningLessonVO.class);
        vo.setCourseName(cinfo.getName());
        vo.setCourseCoverUrl(cinfo.getCoverUrl());
        vo.setSections(cinfo.getSectionNum());
        vo.setCourseAmount(count);
        CataSimpleInfoDTO currentCatalogueInfo = cataSimpleInfoDTOS.get(0);
        vo.setLatestSectionName(currentCatalogueInfo.getName());
        vo.setLatestSectionIndex(currentCatalogueInfo.getCIndex());

        return vo;
    }
}
