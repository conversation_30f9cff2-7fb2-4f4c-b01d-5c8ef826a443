import com.tianji.learning.LearningApplication;
import com.tianji.learning.domain.po.PointsBoard;
import com.tianji.learning.mapper.PointsRecordMapper;
import com.tianji.learning.service.IPointsBoardService;
import com.tianji.learning.utils.TableInfoContext;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@Slf4j
@SpringBootTest(classes = LearningApplication.class)
public class test {

    @Resource
    private IPointsBoardService pointsBoardService;

    @Test
    public void test() {
        System.out.println("=== 开始测试动态表名 ===");
        System.out.println("设置动态表名前: " + TableInfoContext.getInfo());
        TableInfoContext.setInfo("points_board_10");
        System.out.println("设置动态表名后: " + TableInfoContext.getInfo());

        PointsBoard pointsBoard = new PointsBoard();
        pointsBoard.setId(1L);
        pointsBoard.setUserId(2L);
        pointsBoard.setPoints(100);
        // 不设置 rank 和 season，让它们保持 null
        System.out.println("创建的 PointsBoard 对象: " + pointsBoard);

        try {
            System.out.println("=== 尝试使用 MyBatis-Plus save 方法 ===");
            pointsBoardService.save(pointsBoard);
            System.out.println("MyBatis-Plus save 方法保存成功");
        } catch (Exception e) {
            System.out.println("MyBatis-Plus save 方法保存失败: " + e.getMessage());
            System.out.println("完整错误信息:");
            e.printStackTrace();
        } finally {
            System.out.println("清理 ThreadLocal 前: " + TableInfoContext.getInfo());
            TableInfoContext.remove();
            System.out.println("清理 ThreadLocal 后: " + TableInfoContext.getInfo());
            System.out.println("=== 测试结束 ===");
        }
    }
}
