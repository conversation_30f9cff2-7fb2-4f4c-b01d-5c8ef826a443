package com.tianji.promotion.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mysql.cj.x.protobuf.MysqlxDatatypes;
import com.tianji.common.domain.dto.PageDTO;
import com.tianji.common.exceptions.BizIllegalException;
import com.tianji.common.utils.*;
import com.tianji.promotion.constants.PromotionConstants;
import com.tianji.promotion.domain.dto.CouponFormDTO;
import com.tianji.promotion.domain.dto.CouponIssueFormDTO;
import com.tianji.promotion.domain.po.Coupon;
import com.tianji.promotion.domain.po.CouponScope;
import com.tianji.promotion.domain.po.UserCoupon;
import com.tianji.promotion.domain.query.CouponQuery;
import com.tianji.promotion.domain.vo.CouponPageVO;
import com.tianji.promotion.domain.vo.CouponVO;
import com.tianji.promotion.enums.CouponStatus;
import com.tianji.promotion.enums.ObtainType;
import com.tianji.promotion.enums.UserCouponStatus;
import com.tianji.promotion.mapper.CouponMapper;
import com.tianji.promotion.service.ICouponScopeService;
import com.tianji.promotion.service.ICouponService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tianji.promotion.service.IExchangeCodeService;
import com.tianji.promotion.service.IUserCouponService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 优惠券的规则信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CouponServiceImpl extends ServiceImpl<CouponMapper, Coupon> implements ICouponService {

    private final ICouponScopeService couponScopeService;
    private final IExchangeCodeService exchangeCodeService;
    private final IUserCouponService userCouponService;
    private final StringRedisTemplate redisTemplate;

    /**
     * 新增优惠券
     * @param dto 优惠券信息
     */
    @Override
    @Transactional
    public void saveCoupon(CouponFormDTO dto) {
        log.debug("saveCoupon 新增优惠券：{}", dto);
        // 保存优惠券
        Coupon coupon = BeanUtils.copyBean(dto, Coupon.class);
        this.save(coupon);
        // 是否限定使用范围,没有直接保存
        if (!dto.getSpecific()){
            return;
        }
        // 是否限定使用范围
        List<Long> scopes = dto.getScopes();
        if (CollUtils.isEmpty(scopes)) {
            throw new BizIllegalException("请选择优惠券使用范围");
        }
        // 保存优惠券使用范围
        List<CouponScope> couponList = scopes.stream()
                .map(s -> new CouponScope().setCouponId(coupon.getId()).setBizId(s).setType(1))
                .collect(Collectors.toList());

        /*ArrayList<CouponScope> couponList = new ArrayList<>();
        scopes.forEach(s -> {
            CouponScope scope = new CouponScope();
            scope.setCouponId(coupon.getId());
            scope.setBizId(s);
            scope.setType(1);
            couponList.add(scope);
        });*/
        couponScopeService.saveBatch(couponList);
    }

    /**
     * 查询优惠券列表
     * @param query 查询参数
     * @return 优惠券列表
     */
    @Override
    public PageDTO<CouponPageVO> queryCouponPage(CouponQuery query) {
        // 查询优惠券列表
        Page<Coupon> page = this.lambdaQuery()
                .eq(query.getStatus() != null, Coupon::getStatus, query.getStatus())
                .eq(query.getType() != null, Coupon::getDiscountType, query.getType())
                .like(StringUtils.isNotBlank(query.getName()), Coupon::getName, query.getName())
                .page(query.toMpPageDefaultSortByCreateTimeDesc());
        List<Coupon> records = page.getRecords();
        if (CollUtils.isEmpty(records)){
            return PageDTO.empty(page);
        }
        // 封装返回
        List<CouponPageVO> voList = BeanUtils.copyList(records, CouponPageVO.class);
        return PageDTO.of(page, voList);
    }

    /**
     * 发放优惠券
     * @param id 优惠券id
     * @param dto 发放信息
     */
    @Override
    public void issueCoupon(Long id, CouponIssueFormDTO dto) {
        log.debug("issueCoupon 发放优惠券：{}", dto);
        // 校验参数
        if (id == null || !id.equals(dto.getId())){
            throw new BizIllegalException("参数错误");
        }
        Coupon coupon = this.getById(id);
        if (coupon == null){
            throw new BizIllegalException("优惠券不存在");
        }
        // 只有待发放和暂停的优惠券才可发放
        if (coupon.getStatus() != CouponStatus.DRAFT && coupon.getStatus() != CouponStatus.PAUSE){
            throw new BizIllegalException("优惠券状态错误");
        }
        // 修改优惠券状态
        LocalDateTime now = LocalDateTime.now();
        // 是否立即发放
        boolean isBeginIssue = dto.getIssueBeginTime() == null || !dto.getIssueBeginTime().isAfter(now);

        Coupon tmp = BeanUtils.copyBean(dto, Coupon.class);
        if (isBeginIssue){
            tmp.setStatus(CouponStatus.ISSUING);
            tmp.setIssueBeginTime(now);
        } else {
            tmp.setStatus(CouponStatus.UN_ISSUE);
        }
        this.updateById(tmp);
        // 如果优惠券立刻发放，存入到redis
        if (isBeginIssue){
            String key = PromotionConstants.COUPON_CACHE_KEY_PREFIX + id;
            redisTemplate.opsForHash().put(key,"issueBeginTime", String.valueOf(DateUtils.toEpochMilli(now)));
            redisTemplate.opsForHash().put(key,"issueEndTime", String.valueOf(DateUtils.toEpochMilli(dto.getIssueEndTime())));
            redisTemplate.opsForHash().put(key,"totalNum", String.valueOf(coupon.getTotalNum()));
            redisTemplate.opsForHash().put(key,"userLimit", String.valueOf(coupon.getUserLimit()));
        }

        // 如果是指定发放，需要生成优惠券
        if (coupon.getObtainWay() == ObtainType.ISSUE && coupon.getStatus() == CouponStatus.DRAFT){
            coupon.setIssueEndTime(tmp.getIssueEndTime());
            // 异步生成优惠券
            exchangeCodeService.asyncGenerateExchangeCoupon(coupon);
        }
    }

    /**
     * 查询发放中的优惠券
     * @return 优惠券列表
     */
    @Override
    public List<CouponVO> queryIssuingCoupons() {
        log.debug("queryIssuingCoupons 查询发放中的优惠券");
        // 查询db
        List<Coupon> couponList = this.lambdaQuery()
                .eq(Coupon::getStatus, CouponStatus.ISSUING)
                .eq(Coupon::getObtainWay, ObtainType.PUBLIC)
                .list();
        if (CollectionUtil.isEmpty(couponList)){
            return Collections.emptyList();
        }
        // 查询用户优惠券
        // 正在发放的优惠券集合
        Set<Long> couponIds = couponList.stream().map(Coupon::getId).collect(Collectors.toSet());
        // 当前用户正在发放的优惠券记录
        List<UserCoupon> list = userCouponService.lambdaQuery()
                .eq(UserCoupon::getUserId, UserContext.getUser())
                .in(UserCoupon::getCouponId, couponIds)
                .list();
        /*Map<Long, Long> issueMap = new HashMap<>();
        list.forEach(l -> {
            Long num = issueMap.get(l.getCouponId());// 获取当前优惠券的已发放数量
            if (num == null){
                issueMap.put(l.getCouponId(), 1L);
            } else {
                issueMap.put(l.getCouponId(), Long.valueOf(num.intValue() + 1));
            }
        });*/
        // 获取已发放数量
        Map<Long, Long> issueMap = list.stream()
                .collect(Collectors.groupingBy(UserCoupon::getCouponId,
                        Collectors.counting()));
        // 查询已使用的数量
        Map<Long, Long> unuseMap = list.stream()
                .filter(l -> l.getStatus() == UserCouponStatus.UNUSED)
                .collect(Collectors.groupingBy(UserCoupon::getCouponId,
                        Collectors.counting()));

        // 转换为VO
        ArrayList<CouponVO> voList = new ArrayList<>();
        couponList.forEach(c -> {
            CouponVO couponVO = BeanUtils.copyBean(c, CouponVO.class);
            // 判断是否可以领取
            Long issNum = issueMap.getOrDefault(c.getId(), 0L); // 领取数量
            // 计算逻辑: 领取数量 < 总数量 && 领取数量 < 每个人限制领取数量
            boolean available = c.getIssueNum() < c.getTotalNum() && issNum.intValue() < c.getUserLimit();
            couponVO.setAvailable(available);
            // 判断是否可以使用
            // 领取数量 > 0
            boolean received = unuseMap.getOrDefault(c.getId(), 0L) > 0;
            couponVO.setReceived(received);
            voList.add(couponVO);
        });
        return voList;

    }
}
