package com.tianji.promotion.service.impl;

import com.tianji.promotion.constants.PromotionConstants;
import com.tianji.promotion.domain.po.Coupon;
import com.tianji.promotion.domain.po.ExchangeCode;
import com.tianji.promotion.mapper.ExchangeCodeMapper;
import com.tianji.promotion.service.IExchangeCodeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tianji.promotion.utils.CodeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static com.tianji.promotion.constants.PromotionConstants.COUPON_CODE_MAP_KEY;
import static com.tianji.promotion.constants.PromotionConstants.COUPON_RANGE_KEY;

/**
 * <p>
 * 兑换码 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExchangeCodeServiceImpl extends ServiceImpl<ExchangeCodeMapper, ExchangeCode> implements IExchangeCodeService {

    private final StringRedisTemplate redisTemplate;

    /**
     * 异步生成兑换码
     * @param coupon 优惠券
     */
    @Override
    @Async("generateExchangeCodeExecutor")
    public void asyncGenerateExchangeCoupon(Coupon coupon) {
        log.debug("开始生成优惠券兑换码：{}",Thread.currentThread().getName());
        // 获取优惠券发放数量
        Integer totalNum = coupon.getTotalNum();
        // 生成自增id
        Long increment = redisTemplate.opsForValue().increment(PromotionConstants.COUPON_CODE_SERIAL_KEY, totalNum);
        if (increment == null){
            return;
        }
        int maxSerialNum = increment.intValue();
        int begin = maxSerialNum - totalNum + 1;
        // 批量生成兑换码
        List<ExchangeCode> list = new ArrayList<>();
        for (int serialNum = begin; serialNum <= maxSerialNum; serialNum++) {
            String code = CodeUtil.generateCode(serialNum, coupon.getId());
            ExchangeCode exchangeCode = new ExchangeCode();
            exchangeCode.setCode(code);
            exchangeCode.setExchangeTargetId(coupon.getId());// 优惠券id
            exchangeCode.setId(serialNum);
            exchangeCode.setExpiredTime(coupon.getIssueEndTime());
            list.add(exchangeCode);
        }
        // 保存兑换码
        this.saveBatch(list);
        // 写入缓存
        redisTemplate.opsForZSet().add(COUPON_RANGE_KEY , coupon.getId().toString(), maxSerialNum);

    }

    /**
     * 更新兑换码状态
     * @param serialNum
     * @param b
     * @return
     */
    @Override
    public boolean updateExchangeCodeMark(long serialNum, boolean b) {
        // 修改兑换码的id对应的offset值
        Boolean setBit = redisTemplate.opsForValue().setBit(COUPON_CODE_MAP_KEY, serialNum, b);
        return setBit != null && setBit;
    }
}
