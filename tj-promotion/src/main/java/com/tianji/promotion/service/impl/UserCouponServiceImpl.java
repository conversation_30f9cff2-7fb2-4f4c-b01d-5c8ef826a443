package com.tianji.promotion.service.impl;

import cn.hutool.core.bean.copier.CopyOptions;
import com.tianji.common.autoconfigure.mq.RabbitMqHelper;
import com.tianji.common.constants.MqConstants;
import com.tianji.common.exceptions.BadRequestException;
import com.tianji.common.exceptions.BizIllegalException;
import com.tianji.common.utils.BeanUtils;
import com.tianji.common.utils.CollUtils;
import com.tianji.common.utils.StringUtils;
import com.tianji.common.utils.UserContext;
import com.tianji.promotion.constants.PromotionConstants;
import com.tianji.promotion.discount.Discount;
import com.tianji.promotion.discount.DiscountStrategy;
import com.tianji.promotion.domain.dto.CouponDiscountDTO;
import com.tianji.promotion.domain.dto.OrderCourseDTO;
import com.tianji.promotion.domain.dto.UserCouponDTO;
import com.tianji.promotion.domain.po.Coupon;
import com.tianji.promotion.domain.po.CouponScope;
import com.tianji.promotion.domain.po.ExchangeCode;
import com.tianji.promotion.domain.po.UserCoupon;
import com.tianji.promotion.enums.ExchangeCodeStatus;
import com.tianji.promotion.mapper.CouponMapper;
import com.tianji.promotion.mapper.UserCouponMapper;
import com.tianji.promotion.service.ICouponScopeService;
import com.tianji.promotion.service.IExchangeCodeService;
import com.tianji.promotion.service.IUserCouponService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tianji.promotion.utils.CodeUtil;
import com.tianji.promotion.utils.MyLockUtils.MyLock;
import com.tianji.promotion.utils.MyLockUtils.enums.MyLockStrategy;
import com.tianji.promotion.utils.MyLockUtils.enums.MyLockType;
import com.tianji.promotion.utils.PermuteUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户领取优惠券的记录，是真正使用的优惠券信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserCouponServiceImpl extends ServiceImpl<UserCouponMapper, UserCoupon> implements IUserCouponService {

    private final CouponMapper couponMapper;
    private final IExchangeCodeService exchangeCodeService;
    private final StringRedisTemplate redisTemplate;
    private final RedissonClient redisson;
    private final RabbitMqHelper rabbitMqHelper;
    private final ICouponScopeService couponScopeService;

    private final Executor discountSolutionExecutor;

    /**
     * 用户领取优惠券
     * @param id 优惠券id
     */
    @Override
    @MyLock(name = "lock:coupon:uid:#{id}")
    public void receiveCoupon(Long id) {
        log.debug("receiveCoupon 用户领取优惠券：{}", id);
        //1.根据id查询优惠券信息做相关校验
        if(id == null){
            throw new BadRequestException("非法参数");
        }
//        Coupon coupon = couponMapper.selectById(id);
        // 缓存查询
        Coupon coupon = queryCouponByCache(id);
        if(coupon==null){
            throw new BadRequestException("优惠券不存在");
        }
        /*if(coupon.getStatus() != CouponStatus.ISSUING){
            throw new BadRequestException("该优惠券状态不是正在发放");
        }*/
        LocalDateTime now = LocalDateTime.now();
        if (coupon.getIssueBeginTime() == null || coupon.getIssueEndTime() == null
                || now.isBefore(coupon.getIssueBeginTime()) || now.isAfter(coupon.getIssueEndTime())){
            throw new BadRequestException("该优惠券不在发放时间");
        }
        if (coupon.getTotalNum() <= 0){
            throw new BadRequestException("该优惠券已发放完毕");
        }
        Long userId = UserContext.getUser();
        // 获取当前用户已经领取的优惠券
        /*synchronized (userId.toString().intern()) {
            // 从aop 获取service
            IUserCouponService userCouponService = (IUserCouponService) AopContext.currentProxy();
            userCouponService.checkAndCreateUserCoupon(coupon, userId, null);
        }*/
        // 从aop 获取service
        /*IUserCouponService userCouponService = (IUserCouponService) AopContext.currentProxy();
        userCouponService.checkAndCreateUserCoupon(coupon, userId, null);*/

        // 统计已经领取的数量
        String key = PromotionConstants.USER_COUPON_CACHE_KEY_PREFIX + id;
        // 本次领取的数量
        Long increment = redisTemplate.opsForHash().increment(key, userId.toString(), 1);
        // 校验已领取数量
        if (increment > coupon.getUserLimit()){
            throw new BizIllegalException("超出限领数量");
        }
        // 修改优惠券库存
        String couponKey = PromotionConstants.COUPON_CACHE_KEY_PREFIX + id;
        redisTemplate.opsForHash().increment(couponKey,"totalNum",-1);
        // 发送消息
        UserCouponDTO msg = new UserCouponDTO();
        msg.setUserId(userId);
        msg.setCouponId(id);
        rabbitMqHelper.send(MqConstants.Exchange.PROMOTION_EXCHANGE,MqConstants.Key.COUPON_RECEIVE,msg);

    }

    // 缓存查询
    private Coupon queryCouponByCache(Long id) {
        // 拼接key
        String key = PromotionConstants.COUPON_CACHE_KEY_PREFIX + id;
        Map<Object, Object> entries = redisTemplate.opsForHash().entries(key);
        return BeanUtils.mapToBean(entries, Coupon.class, false, CopyOptions.create());
    }

    @Override
//    @Transactional
    public void exchangeCoupon(String code) {
        // 校验code
        if (StringUtils.isBlank(code)){
            throw new BadRequestException("非法参数");
        }
        // 解析兑换码的id
        long serialNum = CodeUtil.parseCode(code);
        // 获取兑换码
        boolean result = exchangeCodeService.updateExchangeCodeMark(serialNum,true);
        if (result){
            throw new BadRequestException("兑换码已使用");
        }
        //
        try {
            // 确认是否存在
            ExchangeCode exchangeCode = exchangeCodeService.getById(serialNum);
            if (exchangeCode == null){
                throw new BadRequestException("兑换码不存在");
            }
            // 是否过期
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime expiredTime = exchangeCode.getExpiredTime();
            if (now.isAfter(expiredTime)){
                throw new BadRequestException("兑换码已过期");
            }
            // 校验并生成用户券
            Long userId = UserContext.getUser();
            Coupon coupon = couponMapper.selectById(exchangeCode.getExchangeTargetId());
            if (coupon == null){
                throw new BadRequestException("兑换码已失效");
            }

            /*// 先开启锁 然后再执行事务
            synchronized (userId.toString().intern()) {
                checkAndCreateUserCoupon(coupon, userId, serialNum);
            }*/
            checkAndCreateUserCoupon(coupon, userId,serialNum);


        } catch (Exception e) {
            exchangeCodeService.updateExchangeCodeMark(serialNum,false);
            throw new RuntimeException(e);
        }
    }

    // 创建用户优惠券
    @Transactional // 控制锁
    @MyLock(name = "lock:coupon:uid:#{userId}",waitTime = 1,leaseTime = 5, LockType = MyLockType.RE_ENTRANT_LOCK,lockStrategy = MyLockStrategy.FAIL_FAST)
    public void checkAndCreateUserCoupon(Coupon coupon, Long userId, Long serialNum) {
        Long count = this.lambdaQuery()
                .eq(UserCoupon::getUserId, userId)
                .eq(UserCoupon::getCouponId, coupon.getId())
                .count();
        if (count >= coupon.getUserLimit()){
            throw new BadRequestException("该优惠券已领取");
        }
        // 增加领取数量
        couponMapper.incrIssueNum(coupon.getId());
        // 生成用户券
        saveUserCoupon(userId, coupon);
        // 修改兑换码
        if (serialNum != null){
            exchangeCodeService.lambdaUpdate()
                    .set(ExchangeCode::getUserId, userId)
                    .set(ExchangeCode::getStatus, ExchangeCodeStatus.USED)
                    .eq(ExchangeCode::getId, serialNum)
                    .update();
        }

    }

    /**
     * 检查并创建用户优惠券
     * @param msg 用户优惠券信息，包含用户ID和优惠券ID
     */
    @Transactional
    @Override
    public void checkAndCreateUserCouponNew(UserCouponDTO msg) {
        Coupon coupon = couponMapper.selectById(msg.getCouponId());
        if (coupon == null){
            return;
        }
        // 增加领取数量
        couponMapper.incrIssueNum(coupon.getId());
        // 生成用户券
        saveUserCoupon(msg.getUserId(), coupon);
        // 修改兑换码
        /*if (serialNum != null){
            exchangeCodeService.lambdaUpdate()
                    .set(ExchangeCode::getUserId, userId)
                    .set(ExchangeCode::getStatus, ExchangeCodeStatus.USED)
                    .eq(ExchangeCode::getId, serialNum)
                    .update();
        }*/
    }

    // 保存用户券
    private void saveUserCoupon(Long userId, Coupon coupon) {

        LocalDateTime termBeginTime = coupon.getTermBeginTime();
        LocalDateTime termEndTime = coupon.getTermEndTime();
        if (termBeginTime == null && termEndTime == null){
            termBeginTime = LocalDateTime.now();
            termEndTime = termBeginTime.plusDays(coupon.getTermDays());
        }
        // 创建用户券
        UserCoupon userCoupon = UserCoupon.builder()
                .userId(userId)
                .couponId(coupon.getId())
                .termBeginTime(termBeginTime)
                .termEndTime(termEndTime)
                .build();

        this.save(userCoupon);
    }

    /**
     * 查询可用优惠券方案
     * @param courses
     * @return
     */
    @Override
    public List<CouponDiscountDTO> findDiscountSolution(List<OrderCourseDTO> courses) {
        // 查询当前用户的可用优惠券
        List<Coupon> coupons = getBaseMapper().queryMyCoupon(UserContext.getUser());
        if (CollUtils.isEmpty(coupons)){
            return CollUtils.emptyList();
        }
        log.debug("findDiscountSolution 查询可用优惠券方案 共有 {}", coupons.size());
        coupons.forEach(coupon -> {
            log.debug("优惠券：{}，{}",
                    DiscountStrategy.getDiscount(coupon.getDiscountType()).getRule(coupon),
                    coupon
                    );
        });
        // 初筛 计算总金额
        int totalAmount = courses.stream().mapToInt(OrderCourseDTO::getPrice).sum();
        log.debug("订单总金额为 {}", totalAmount);
        // 校验优惠券是否可用
        /*List<Coupon> availableCoupons = new ArrayList<>();
        availableCoupons.forEach(coupon -> {
            boolean flag = DiscountStrategy.getDiscount(coupon.getDiscountType()).canUse(totalAmount, coupon);
            if (flag){
                availableCoupons.add(coupon);
            }
        });*/
        List<Coupon> availableCoupons = coupons.stream()
                .filter(coupon -> DiscountStrategy.getDiscount(coupon.getDiscountType()).canUse(totalAmount, coupon))
                .collect(Collectors.toList());
        if (CollUtils.isEmpty(availableCoupons)){
            return CollUtils.emptyList();
        }
        log.debug("筛选后可用优惠券方案 {}", availableCoupons.size());
        availableCoupons.forEach(coupon -> {
            log.debug("优惠券：{}，{}",
                    DiscountStrategy.getDiscount(coupon.getDiscountType()).getRule(coupon),
                    coupon
            );
        });
        // 细筛
        Map<Coupon,List<OrderCourseDTO>> avaMap = findAvailableCoupon(courses, availableCoupons);
        if (avaMap.isEmpty()){
            return CollUtils.emptyList();
        }
        Set<Map.Entry<Coupon, List<OrderCourseDTO>>> entries = avaMap.entrySet();
        entries.forEach(entry ->{
            log.debug("细筛之后 {},{}",
                    DiscountStrategy.getDiscount(entry.getKey().getDiscountType()).getRule(entry.getKey()),
                    entry.getKey()
            );
            entry.getValue().forEach(course -> {
                log.debug("可用课程 {}", course);
            });
        });
        coupons = new ArrayList<>(avaMap.keySet());
        log.debug("细筛之后的个数 {}", coupons.size());
        coupons.forEach(coupon -> {
            log.debug("优惠券：{}，{}",
                    DiscountStrategy.getDiscount(coupon.getDiscountType()).getRule(coupon),
                    coupon
            );
        });
        // 排列组合
        List<List<Coupon>> solutions = PermuteUtil.permute(coupons);
        coupons.forEach(coupon -> {
            solutions.add(List.of(coupon));
        });
        log.debug("排列组合之后的个数");
        solutions.forEach(solution -> {
            List<Long> cids = solution.stream().map(Coupon::getId).collect(Collectors.toList());
            log.debug("{}",cids);
        });
        // 计算组合的优惠明细
        /*List<CouponDiscountDTO> dtos = new ArrayList<>();
        for (List<Coupon> solution : solutions) {
            dtos.add(calculateSolutionDiscount(avaMap, courses, solution));
        }*/

//        List<CouponDiscountDTO> dtos = new ArrayList<>();
        List<CouponDiscountDTO> dtos = Collections.synchronizedList(new ArrayList<>(solutions.size()));
        CountDownLatch latch = new CountDownLatch(solutions.size());
        // 计算优惠券组合的优惠明细
        solutions.forEach(solution ->{
            CompletableFuture.supplyAsync(
                    () -> calculateSolutionDiscount(avaMap, courses, solution),discountSolutionExecutor)
                    .thenAccept(dto -> { dtos.add(dto); latch.countDown();});
        });
        try {
            latch.await(2, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.error("计算优惠券组合的优惠明细超时");
        }

        // 5.筛选最优解
        return findBestSolution(dtos);
    }

    private List<CouponDiscountDTO> findBestSolution(List<CouponDiscountDTO> list) {
        // 1.准备Map记录最优解
        Map<String, CouponDiscountDTO> moreDiscountMap = new HashMap<>();
        Map<Integer, CouponDiscountDTO> lessCouponMap = new HashMap<>();
        // 2.遍历，筛选最优解
        for (CouponDiscountDTO solution : list) {
            // 2.1.计算当前方案的id组合
            String ids = solution.getIds().stream()
                    .sorted(Long::compare).map(String::valueOf).collect(Collectors.joining(","));
            // 2.2.比较用券相同时，优惠金额是否最大
            CouponDiscountDTO best = moreDiscountMap.get(ids);
            if (best != null && best.getDiscountAmount() >= solution.getDiscountAmount()) {
                // 当前方案优惠金额少，跳过
                continue;
            }
            // 2.3.比较金额相同时，用券数量是否最少
            best = lessCouponMap.get(solution.getDiscountAmount());
            int size = solution.getIds().size();
            if (size > 1 && best != null && best.getIds().size() <= size) {
                // 当前方案用券更多，放弃
                continue;
            }
            // 2.4.更新最优解
            moreDiscountMap.put(ids, solution);
            lessCouponMap.put(solution.getDiscountAmount(), solution);
        }
        // 3.求交集
        Collection<CouponDiscountDTO> bestSolutions = CollUtils
                .intersection(moreDiscountMap.values(), lessCouponMap.values());
        // 4.排序，按优惠金额降序
        return bestSolutions.stream()
                .sorted(Comparator.comparingInt(CouponDiscountDTO::getDiscountAmount).reversed())
                .collect(Collectors.toList());
    }

    /**
     * 计算优惠券组合的优惠明细
     * @param avaMap
     * @param courses
     * @param solution
     * @return
     */
    private CouponDiscountDTO calculateSolutionDiscount(Map<Coupon, List<OrderCourseDTO>> avaMap, List<OrderCourseDTO> courses, List<Coupon> solution) {

        // 1.初始化DTO
        CouponDiscountDTO dto = new CouponDiscountDTO();
        // 2.初始化折扣明细的映射
        Map<Long, Integer> detailMap = courses.stream().collect(Collectors.toMap(OrderCourseDTO::getId, oc -> 0));
        // 3.计算折扣
        for (Coupon coupon : solution) {
            // 3.1.获取优惠券限定范围对应的课程
            List<OrderCourseDTO> availableCourses = avaMap.get(coupon);
            // 3.2.计算课程总价(课程原价 - 折扣明细)
            int totalAmount = availableCourses.stream()
                    .mapToInt(oc -> oc.getPrice() - detailMap.get(oc.getId())).sum();
            // 3.3.判断是否可用
            Discount discount = DiscountStrategy.getDiscount(coupon.getDiscountType());
            if (!discount.canUse(totalAmount, coupon)) {
                // 券不可用，跳过
                continue;
            }
            // 3.4.计算优惠金额
            int discountAmount = discount.calculateDiscount(totalAmount, coupon);
            // 3.5.计算优惠明细
            calculateDiscountDetails(detailMap, availableCourses, totalAmount, discountAmount);
            // 3.6.更新DTO数据
            dto.getIds().add(coupon.getCreater());
            dto.getRules().add(discount.getRule(coupon));
            dto.setDiscountAmount(discountAmount + dto.getDiscountAmount());
        }
        return dto;
    }

    private void calculateDiscountDetails(Map<Long, Integer> detailMap, List<OrderCourseDTO> courses,
                                          int totalAmount, int discountAmount) {
        int times = 0;
        int remainDiscount = discountAmount;
        for (OrderCourseDTO course : courses) {
            // 更新课程已计算数量
            times++;
            int discount = 0;
            // 判断是否是最后一个课程
            if (times == courses.size()) {
                // 是最后一个课程，总折扣金额 - 之前所有商品的折扣金额之和
                discount = remainDiscount;
            } else {
                // 计算折扣明细（课程价格在总价中占的比例，乘以总的折扣）
                discount = discountAmount * course.getPrice() / totalAmount;
                remainDiscount -= discount;
            }
            // 更新折扣明细
            detailMap.put(course.getId(), discount + detailMap.get(course.getId()));
        }
    }

    /**
     * 细筛
     * @param courses
     * @param coupons
     * @return
     */
    private Map<Coupon, List<OrderCourseDTO>> findAvailableCoupon(List<OrderCourseDTO> courses, List<Coupon> coupons) {
        Map<Coupon, List<OrderCourseDTO>> map = new HashMap<>();
        // 循环遍历初筛集合
        for (Coupon coupon : coupons) {
            List<OrderCourseDTO> availableCourses = courses;
            // 判断是否限定使用范围
            if (coupon.getSpecific()){
                // 有使用范围，查询优惠券的使用范围
                List<CouponScope> scopeList = couponScopeService.lambdaQuery()
                        .eq(CouponScope::getCouponId, coupon.getId())
                        .list();
                // 得到限定范围的课程id
                List<Long> scopeIds = scopeList.stream().map(CouponScope::getBizId).collect(Collectors.toList());
                // 筛选出课程
                availableCourses = courses.stream()
                        .filter(c -> scopeIds.contains(c.getCateId()))
                        .collect(Collectors.toList());
            }
            if (CollUtils.isEmpty(availableCourses)){
                continue;
            }
            // 计算优惠金额
            int totalAmount = availableCourses.stream().mapToInt(OrderCourseDTO::getPrice).sum();
            // 判断是否可以使用
            Discount discount = DiscountStrategy.getDiscount(coupon.getDiscountType());
            if (discount.canUse(totalAmount, coupon)){
                // 可以使用
                map.put(coupon, availableCourses);
            }
        }
        return map;
    }
}
